/* Font Definitions */
@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    /* Optimize scrolling performance */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Lora', serif;
    line-height: 1.6;
    color: #333;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    /* Enable hardware acceleration for better scrolling */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Header */
.header {
    background-color: #222222;
    padding: 15px 20px;
    text-align: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 300;
    color: #7c6cd8;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.3s ease;
    margin: 0;
}

.header h1:hover {
    color: #6a5bc7;
}

/* Hamburger Menu Button */
.hamburger-menu {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background-color: #7c6cd8;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.hamburger-menu:hover .hamburger-line {
    background-color: #fdc950;
}

/* Navigation Menu */
.nav-menu {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    background-color: #222222;
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.nav-menu.active {
    left: 0;
}

/* Responsive menu width */
@media (max-width: 480px) {
    .nav-menu {
        width: 100vw;
        left: -100vw;
    }

    .nav-menu.active {
        left: 0;
    }
}

@media (max-width: 768px) and (min-width: 481px) {
    .nav-menu {
        width: 80vw;
        left: -80vw;
    }

    .nav-menu.active {
        left: 0;
    }
}

.nav-menu-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.nav-links {
    list-style: none;
    padding: 0;
    margin: 40px 0 0 0;
}

.nav-links li {
    margin-bottom: 20px;
}

.nav-link {
    display: block;
    color: #ffffff;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 18px;
    padding: 15px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.nav-link:hover {
    background-color: #7c6cd8;
    color: white;
    border-left-color: #fdc950;
    transform: translateX(5px);
}

/* Overlay for menu */
.nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Banner */
.banner {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.banner h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #ffffff;
    font-size: 2.5rem;
    z-index: 1;
    position: relative;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-zwischenmenschlichkeit {
    background-image: url('assets/bilder/zwischenmenschlichkeit.png');
}

.banner-zusammenhalt {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-fuehrung {
    background-image: url('assets/bilder/fuehrung.png');
}

.banner-blumenfeld {
    background-image: url('assets/bilder/blumenfeld.png');
}

.banner-weekend {
    background-image: url('assets/bilder/weekend.png');
}

/* Unified Section Box - Beautiful consistent styling for all sections */
.section-box {
    max-width: 800px;
    margin: 40px auto;
    padding: 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    line-height: 1.8;
    position: relative;
}

.section-box p {
    margin-bottom: 15px;
    font-size: 16px;
}

.section-box strong {
    font-weight: 700;
}

.section-box h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #7c6cd8;
    font-size: 24px;
    margin: 20px 0px 10px 0px;
}

.section-box ul {
    margin: 20px 0;
    padding-left: 30px;
}

.section-box li {
    margin-bottom: 10px;
    font-size: 16px;
}

/* Community Section - centered text */
.section-box.community {
    text-align: center;
}

/* Profile elements in section-box - centered styling */
.section-box .profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 5px solid #fdc950;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0 auto 20px auto;
    display: block;
}

.section-box .profile-image:hover {
    transform: scale(1.05);
}

.section-box .profile-title {
    font-family: 'Caveat', cursive;
    color: #fdc950;
    font-size: 24px;
    margin-bottom: 30px;
    text-align: center;
}

/* Einheitsbutton - Unified button styling for all buttons */
.einheitsbutton {
    display: block;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: auto;
    width: fit-content;
    text-align: center;
}

/* Active state for einheitsbutton */
.einheitsbutton.active {
    background-color: #7c6cd8;
    color: white;
}

.einheitsbutton.active:hover {
    background-color: #6a5bc7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(124, 108, 216, 0.3);
}

/* Inactive state for einheitsbutton */
.einheitsbutton.inactive {
    background-color: #ccc;
    color: #666;
    pointer-events: none;
}

/* Events Text - simple text without box styling */
.events-text {
    max-width: 800px;
    margin: 40px auto;
    padding: 0 30px;
    text-align: center;
    line-height: 1.8;
}

.events-text p {
    margin-bottom: 15px;
    font-size: 16px;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.event-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.event-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 10px;
}

.event-description {
    font-size: 14px;
    margin-bottom: 15px;
    color: #666;
    flex-grow: 1;
}

.event-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 15px;
}

.status-available {
    background-color: #4CAF50;
    color: white;
}

.status-coming {
    background-color: #E91E63;
    color: white;
}



/* Profile Section (Jana Sophie Page) - extends section-box with special positioning */
.profile-section {
    max-width: 800px;
    margin: -50px auto 40px;
    padding: 80px 30px 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
    z-index: 2;
    line-height: 1.8;
}

.profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 5px solid #fdc950;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.profile-image:hover {
    transform: scale(1.05);
}

.profile-title {
    font-family: 'Caveat', cursive;
    color: #fdc950;
    font-size: 24px;
    margin-bottom: 30px;
}

.profile-content {
    text-align: left;
    margin-top: 30px;
}

.profile-content h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #7c6cd8;
    font-size: 24px;
    margin-bottom: 20px;
    margin-top: 30px;
}

.profile-content h3:first-child {
    margin-top: 0;
}

.profile-content p {
    margin-bottom: 15px;
    font-size: 16px;
    line-height: 1.8;
}

/* Strengths Section - now uses section-box styling */

.strengths-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 20px 0px 30px 0px;
}

.strength-tag {
    display: inline-block;
    padding: 10px 20px;
    border: 2px solid #7c6cd8;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    color: #7c6cd8;
    background: white;
    transition: all 0.3s ease;
}

.strength-tag:hover {
    background: #7c6cd8;
    color: white;
    transform: scale(1.05);
}

/* Social Media Section - Classic platform-specific styling */

.social-links {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
}

.social-link {
    display: inline-block;
    padding: 10px 20px;
    border: 2px solid;
    border-radius: 25px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    background: white;
    text-decoration: none;
    transition: all 0.3s ease;
    text-align: center;
}

/* Instagram - Classic gradient border */
.social-link[href*="instagram"] {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: #e6683c;
}

.social-link[href*="instagram"]:hover {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: white;
    transform: scale(1.05);
}

/* YouTube - Classic red */
.social-link[href*="youtube"] {
    border-color: #FF0000;
    color: #FF0000;
}

.social-link[href*="youtube"]:hover {
    background-color: #FF0000;
    color: white;
    transform: scale(1.05);
}

/* Spotify - Classic green */
.social-link[href*="spotify"] {
    border-color: #1DB954;
    color: #1DB954;
}

.social-link[href*="spotify"]:hover {
    background-color: #1DB954;
    color: white;
    transform: scale(1.05);
}

/* Telegram - Classic blue */
.social-link[href*="t.me"], .social-link[href*="telegram"] {
    border-color: #0088cc;
    color: #0088cc;
}

.social-link[href*="t.me"]:hover, .social-link[href*="telegram"]:hover {
    background-color: #0088cc;
    color: white;
    transform: scale(1.05);
}

/* Weekend Page Specific Styles */
.highlights-box {
    background-color: #f8f9fa;
    border-left: 4px solid #7c6cd8;
    padding: 25px;
    margin: 30px 0;
    border-radius: 5px;
}

.highlights-box h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #7c6cd8;
    margin-bottom: 15px;
    font-size: 20px;
}

.details-box {
    background-color: #7c6cd8;
    color: white;
    padding: 30px;
    margin: 30px 0;
    border-radius: 10px;
}

.details-box h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 22px;
    color: white;
}

.details-box ul {
    list-style: none;
    padding: 0;
}

.details-box li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.details-box li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #fdc950;
    font-weight: bold;
    font-size: 20px;
}

.participation-box {
    background: linear-gradient(135deg, #7c6cd8, #6a5bc7);
    color: white;
    padding: 30px;
    margin: 40px 0;
    border-radius: 15px;
    text-align: left;
    box-shadow: 0 8px 25px rgba(124, 108, 216, 0.3);
}

.participation-box h3 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 24px;
    color: white;
}

.participation-box p {
    font-size: 16px;
    margin-bottom: 0;
}

.signature {
    text-align: right;
    margin-top: 40px;
    font-style: italic;
    color: #7c6cd8;
    font-size: 18px;
}

.signature a {
    color: #7c6cd8;
    text-decoration: none;
    font-style: italic;
}

.signature a:hover {
    color: #fdc950;
    text-decoration: none;
}

/* Impressum Page Specific Styles - now uses section-box styling */
.section-box h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #222222;
    font-size: 28px;
    margin-bottom: 30px;
    border-bottom: 2px solid #7c6cd8;
    padding-bottom: 10px;
}

.contact-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    margin: 20px 0;
}



/* Footer */
.footer {
    background-color: #222222;
    padding: 20px 0;
    text-align: center;
    margin-top: 60px;
}

.footer p {
    font-family: 'Montserrat', sans-serif;
    color: #7c6cd8;
    font-size: 14px;
}

.footer a {
    color: #7c6cd8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    text-decoration: underline;
    color: #fdc950;
}

/* Scrolling Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

/* Smooth scrollbar styling for better UX */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(124, 108, 216, 0.6);
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(124, 108, 216, 0.8);
}

/* Mobile-specific optimizations */
.mobile-device .einheitsbutton:hover,
.mobile-device .social-link:hover,
.mobile-device .event-card:hover,
.mobile-device .strength-tag:hover,
.mobile-device .profile-image:hover {
    transform: none !important;
    box-shadow: none !important;
    background-color: inherit !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .header {
        padding: 10px 0;
    }

    .header h1 {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .header-nav {
        gap: 20px;
    }

    .nav-link {
        font-size: 13px;
        padding: 6px 12px;
    }

    .banner h2 {
        font-size: 1.8rem;
    }

    .section-box {
        margin: 20px;
        padding: 20px;
    }

    .events-text {
        margin: 20px;
        padding: 0 20px;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .banner {
        height: 300px;
    }

    .profile-section {
        margin: -30px 20px 40px;
        padding: 60px 20px 30px;
    }

    .profile-image {
        width: 150px;
        height: 150px;
    }

    .profile-title {
        font-size: 20px;
    }

    .social-links {
        flex-direction: column;
        align-items: center;
    }

    .strengths-grid {
        justify-content: center;
    }

    .section-box h2 {
        font-size: 24px;
    }

    .highlights-box,
    .details-box,
    .participation-box {
        margin: 20px 0;
        padding: 20px;
    }

    .details-box h3,
    .participation-box h3 {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .header-nav {
        gap: 15px;
        flex-wrap: wrap;
    }

    .nav-link {
        font-size: 12px;
        padding: 5px 10px;
    }

    .banner h2 {
        font-size: 1.5rem;
    }

    .section-box {
        margin: 10px;
        padding: 15px;
    }

    .events-text {
        margin: 10px;
        padding: 0 15px;
    }

    .banner {
        height: 250px;
    }

    .section-box h3 {
        font-size: 18px;
    }
}