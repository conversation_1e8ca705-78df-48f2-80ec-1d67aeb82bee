// Main JavaScript file for DAS NEUE JETZT website

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// Initialize all website functionality
function initializeWebsite() {
    // Header navigation
    setupHeaderNavigation();

    // Auto-hide header on scroll
    setupAutoHideHeader();

    // Hamburger menu
    setupHamburgerMenu();

    // Profile image modal (if on Jana Sophie page)
    setupProfileImageModal();

    // Optimize for mobile devices
    optimizeForMobile();
}

// Header Navigation
function setupHeaderNavigation() {
    const headerTitle = document.querySelector('.header h1');
    if (headerTitle) {
        headerTitle.addEventListener('click', function() {
            window.location.href = 'index.html';
        });

        // Add cursor pointer style
        headerTitle.style.cursor = 'pointer';
    }

    // Set active navigation link based on current page
    setActiveNavigation();
}

// Set active navigation link
function setActiveNavigation() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.classList.remove('active');
        const linkHref = link.getAttribute('href');

        if (linkHref === currentPage ||
            (currentPage === '' && linkHref === 'index.html') ||
            (currentPage === 'index.html' && linkHref === 'index.html')) {
            link.classList.add('active');
        }
    });
}

// Auto-hide header on scroll
function setupAutoHideHeader() {
    const header = document.querySelector('.header');
    if (!header) return;

    let lastScrollTop = 0;
    let scrollThreshold = 10; // Minimum scroll distance to trigger hide/show
    let isScrolling = false;

    function handleScroll() {
        if (isScrolling) return;

        isScrolling = true;
        requestAnimationFrame(() => {
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Don't hide header when at the very top of the page
            if (currentScrollTop <= 0) {
                header.classList.remove('hidden');
                isScrolling = false;
                return;
            }

            // Check if we've scrolled enough to trigger the effect
            const scrollDifference = Math.abs(currentScrollTop - lastScrollTop);
            if (scrollDifference < scrollThreshold) {
                isScrolling = false;
                return;
            }

            // Scrolling down - hide header
            if (currentScrollTop > lastScrollTop && currentScrollTop > 100) {
                header.classList.add('hidden');
            }
            // Scrolling up - show header
            else if (currentScrollTop < lastScrollTop) {
                header.classList.remove('hidden');
            }

            lastScrollTop = currentScrollTop;
            isScrolling = false;
        });
    }

    // Add scroll event listener with throttling
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Also show header when hamburger menu is opened
    const hamburgerMenu = document.getElementById('hamburgerMenu');
    if (hamburgerMenu) {
        hamburgerMenu.addEventListener('click', function() {
            header.classList.remove('hidden');
        });
    }
}

// Hamburger Menu Setup
function setupHamburgerMenu() {
    const hamburgerMenu = document.getElementById('hamburgerMenu');
    const navMenu = document.getElementById('navMenu');

    if (!hamburgerMenu || !navMenu) return;

    // Create overlay element
    const overlay = document.createElement('div');
    overlay.className = 'nav-overlay';
    overlay.id = 'navOverlay';
    document.body.appendChild(overlay);

    // Open menu
    function openMenu() {
        navMenu.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close menu
    function closeMenu() {
        navMenu.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    // Toggle menu on hamburger click
    hamburgerMenu.addEventListener('click', function() {
        if (navMenu.classList.contains('active')) {
            closeMenu();
        } else {
            openMenu();
        }
    });

    // Close menu when clicking overlay
    overlay.addEventListener('click', closeMenu);

    // Close menu when clicking nav links
    const navLinks = navMenu.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', closeMenu);
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && navMenu.classList.contains('active')) {
            closeMenu();
        }
    });
}

// Profile Image Modal (for Jana Sophie page)
function setupProfileImageModal() {
    const profileImage = document.querySelector('.profile-image');

    if (profileImage) {
        // Container für das Modal-Bild erstellen
        const imageModalContainer = document.createElement('div');
        imageModalContainer.className = 'profile-image-modal';
        imageModalContainer.style.display = 'none';
        document.documentElement.appendChild(imageModalContainer);

        // CSS für Modal-Bild hinzufügen
        const style = document.createElement('style');
        style.textContent = `
            .profile-image-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100vw;
                height: 100vh;
                background-color: rgba(0, 0, 0, 0.8);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 0;
            }

            .modal-image-container {
                position: relative;
                z-index: 2001;
            }

            .profile-image-modal .modal-image {
                width: 500px;
                height: 500px;
                max-width: 80vw;
                max-height: 80vh;
                border-radius: 50%;
                object-fit: cover;
                border: 6px solid #fdc950;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
                cursor: default;
            }

            .profile-image-modal .modal-close {
                position: absolute;
                top: -10px;
                right: -10px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 24px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .profile-image-modal .modal-close:hover {
                background: rgba(0, 0, 0, 0.9);
            }

            @media (max-width: 768px) {
                .profile-image-modal .modal-image {
                    width: 350px;
                    height: 350px;
                    max-width: 85vw;
                    max-height: 85vh;
                }
            }

            @media (max-width: 480px) {
                .profile-image-modal .modal-image {
                    width: 300px;
                    height: 300px;
                    max-width: 90vw;
                    max-height: 90vh;
                }
            }
        `;
        document.head.appendChild(style);

        // Klick-Ereignis für das Profilbild hinzufügen
        profileImage.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Modal-Container mit Bild füllen und anzeigen
            imageModalContainer.innerHTML = `
                <div class="modal-image-container">
                    <img src="${profileImage.src}" alt="${profileImage.alt}" class="modal-image">
                    <button class="modal-close" aria-label="Schließen">&times;</button>
                </div>`;

            // Prevent page shift by adding padding to compensate for scrollbar
            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
            document.body.style.paddingRight = scrollbarWidth + 'px';
            document.body.style.overflow = 'hidden';

            imageModalContainer.style.display = 'flex';

            // Event-Handler für das Schließen hinzufügen
            function imageModalClickHandler(e) {
                // Nicht schließen, wenn auf das Bild oder den Schließen-Button geklickt wurde
                if (e.target.classList.contains('modal-image') || e.target.classList.contains('modal-close')) {
                    if (e.target.classList.contains('modal-close')) {
                        closeImageModal();
                    }
                    return;
                }

                // Schließen bei Klick auf Hintergrund
                closeImageModal();
            }

            imageModalContainer.addEventListener('click', imageModalClickHandler);

            // Event-Handler für den Schließen-Button
            const closeButton = imageModalContainer.querySelector('.modal-close');
            if (closeButton) {
                closeButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    closeImageModal();
                });
            }

            // Funktion zum Schließen des Bild-Modals
            function closeImageModal() {
                imageModalContainer.style.display = 'none';

                // Restore scrolling and remove padding
                document.body.style.overflow = 'auto';
                document.body.style.paddingRight = '';

                // Event-Listener entfernen
                imageModalContainer.removeEventListener('click', imageModalClickHandler);
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && imageModalContainer.style.display === 'flex') {
                imageModalContainer.style.display = 'none';
                document.body.style.overflow = 'auto';
                document.body.style.paddingRight = '';
            }
        });
    }
}









// Handle window resize for responsive elements
window.addEventListener('resize', function() {
    // Close profile image modal if open and screen size changes significantly
    const profileImageModal = document.querySelector('.profile-image-modal');
    if (profileImageModal && profileImageModal.style.display === 'flex') {
        if (window.innerWidth < 768) {
            profileImageModal.style.display = 'none';
            document.body.style.overflow = 'auto';
            document.body.style.paddingRight = '';
        }
    }
}, { passive: true });

// Optimize performance for mobile devices
function optimizeForMobile() {
    // Detect mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        // Add mobile class for CSS optimizations
        document.body.classList.add('mobile-device');

        // Optimize touch scrolling
        document.body.style.webkitOverflowScrolling = 'touch';

        // Prevent zoom on double tap for better UX
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, { passive: false });
    }
}